import streamlit as st
from news_fetch import fetch_stock_news_marketaux
from sentiment_analyzer import analyze_sentiment

POSITIVE_SENTIMENT_THRESHOLD = 0.2  # Define your positive sentiment threshold

st.title("Sentiment-Driven Stock Scanner")

def get_sentiment_label(score):
    if score > POSITIVE_SENTIMENT_THRESHOLD:
        return "Good"
    elif score < -POSITIVE_SENTIMENT_THRESHOLD:
        return "Bad"
    else:
        return "Neutral"

stock_tickers_input = st.text_input(
    "Enter Stock Tickers (comma-separated, e.g., AAPL,TSLA,GOOGL):", ""
).strip()
scan_button = st.button("Scan Stocks")

if scan_button:
    if not stock_tickers_input:
        st.error("Please enter at least one stock ticker.")
    else:
        tickers = [ticker.strip() for ticker in stock_tickers_input.upper().split(',')]
        st.info(f"Scanning sentiment for tickers: {', '.join(tickers)}...")
        stocks_sentiment_results = []

        with st.spinner("Fetching news and analyzing sentiment..."):
            for ticker in tickers:
                news_headlines = fetch_stock_news_marketaux([ticker])  # Pass ticker as a list

                if news_headlines:
                    stock_sentiment_scores = []
                    for headline in news_headlines:
                        sentiment_score = analyze_sentiment(headline)
                        stock_sentiment_scores.append(sentiment_score)

                    average_sentiment = (
                        sum(stock_sentiment_scores) / len(stock_sentiment_scores)
                        if stock_sentiment_scores else 0
                    )
                    sentiment_label = get_sentiment_label(average_sentiment)

                    stocks_sentiment_results.append({
                        "ticker": ticker,
                        "average_sentiment": average_sentiment,
                        "sentiment_label": sentiment_label,
                        "headlines": news_headlines
                    })
                else:
                    st.warning(f"Could not fetch news for {ticker}.")

        if stocks_sentiment_results:
            st.success("Stock Sentiment Results:")
            for stock_data in stocks_sentiment_results:
                st.subheader(f"{stock_data['ticker']}")
                st.metric("Average Sentiment Score", f"{stock_data['average_sentiment']:.4f}")
                st.write(f"**Sentiment:** {stock_data['sentiment_label']}")
                st.write("**Recent Headlines:**")
                for headline in stock_data['headlines']:
                    st.write(f"- {headline}")
                st.markdown("---")  # Separator
        else:
            st.info("No news found for the entered tickers.")